<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ProcessedQuestionData } from '../../utils/question-data-processor'
import SingleChoice from './components/q-single-choice/index'

defineOptions({
  name: 'RightView',
})

// 定义props
const props = defineProps<{
  questionsList: ProcessedQuestionData[]
  currentProgress: { current: number, total: number, description: string }
  isGenerating: boolean
  generationError: string
}>()

// 是否显示题目结果
const showQuestionResult = computed(() => props.questionsList.length > 0 || props.isGenerating)

// 生成标题信息
const titleInfo = computed(() => {
  if (props.isGenerating) {
    return props.currentProgress.description || '正在生成题目...'
  }
  if (props.questionsList.length > 0) {
    const typeCount = props.questionsList.reduce((acc, question) => {
      acc[question.type] = (acc[question.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const typeDescriptions = Object.entries(typeCount)
      .map(([type, count]) => `${count} 道 ${type}`)
      .join(' ')

    return `AI一共为您生成了 ${typeDescriptions}`
  }
  return ''
})

// 选中的答案（为每个题目维护独立的选中状态）
const selectedAnswers = ref<Record<string, string>>({})

// 操作方法
function clearAll() {
  console.log('一键清空')
}

function addToBank() {
  console.log('加入题库')
}

function replaceQuestion() {
  console.log('换一题')
}

function addToTest() {
  console.log('加入题库')
}

function editQuestion() {
  console.log('编辑')
}

function deleteQuestion() {
  console.log('删除')
}
</script>

<template>
  <div class="h-full w-full bg-white p-12px">
    <div v-if="showQuestionResult" class="h-full">
      <NScrollbar class="h-full">
        <!-- 顶部标题栏 -->
        <div class="mb-6 flex items-center justify-between border-b border-gray-100 pb-4">
          <div class="flex items-center gap-2">
            <div class="h-4 w-4 rounded-sm bg-blue-500" />
            <span class="text-lg text-gray-800 font-medium">本次出题结果</span>
          </div>
          <div class="flex gap-3">
            <NButton
              quaternary
              type="primary"
              class="border border-blue-400 rounded-full text-blue-500 hover:bg-blue-50"
              @click="clearAll"
            >
              一键清空
            </NButton>
            <NButton
              type="primary"
              class="rounded-full"
              @click="addToBank"
            >
              加入题库
            </NButton>
          </div>
        </div>

        <!-- AI生成信息 -->
        <div class="mb-6 rounded-lg bg-gray-50 px-4 py-3 text-gray-600">
          {{ titleInfo }}
        </div>

        <!-- 生成进度 -->
        <div v-if="isGenerating" class="mb-6">
          <NProgress
            type="line"
            :percentage="currentProgress.total > 0 ? (currentProgress.current / currentProgress.total) * 100 : 0"
            :show-indicator="true"
          />
          <div class="mt-2 text-sm text-gray-600">
            {{ currentProgress.description }}
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="generationError" class="mb-6 rounded-lg bg-red-50 px-4 py-3 text-red-600">
          生成错误：{{ generationError }}
        </div>

        <!-- 题目卡片列表 -->
        <div v-for="(question, index) in questionsList" :key="question.id" class="mb-6">
          <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
            <!-- 题目类型和操作按钮 -->
            <div class="mb-4 flex items-center justify-between">
              <div class="flex items-center gap-2">
                <NCheckbox />
                <span class="text-gray-600">{{ question.type }}</span>
                <span class="text-sm text-gray-400">第{{ index + 1 }}题</span>
              </div>
              <div class="flex items-center gap-4 text-blue-500">
                <NButton text class="flex items-center gap-1" type="primary" @click="replaceQuestion">
                  <SvgIcon icon="mdi:refresh" class="h-4 w-4" />
                  换一题
                </NButton>
                <NButton text class="flex items-center gap-1" type="primary" @click="addToTest">
                  <SvgIcon icon="mdi:plus" class="h-4 w-4" />
                  加入题库
                </NButton>
                <NButton text class="flex items-center gap-1" type="primary" @click="editQuestion">
                  <SvgIcon icon="mdi:pencil" class="h-4 w-4" />
                  编辑
                </NButton>
                <NButton text class="flex items-center gap-1" type="primary" @click="deleteQuestion">
                  <SvgIcon icon="mdi:delete" class="h-4 w-4" />
                  删除
                </NButton>
              </div>
            </div>

            <!-- 题目内容 -->
            <div class="mb-6 text-gray-800 leading-relaxed">
              {{ question.content }}
            </div>

            <!-- 选项 - 根据题型动态渲染组件 -->
            <div v-if="question.options" class="mb-6">
              <SingleChoice
                v-model="selectedAnswers[question.id]"
                :options="question.options"
                :disabled="true"
              />
            </div>

            <!-- 正确答案 -->
            <div class="mb-6">
              <span class="text-green-600 font-medium">正确答案：{{ question.correctAnswer }}</span>
            </div>

            <!-- 答案解析 -->
            <div class="mb-6 flex">
              <div class="mb-2 text-gray-800 font-medium">
                {{ question.analysis.title }}
              </div>
              <div class="space-y-1">
                <div
                  v-for="(item, analysisIndex) in question.analysis.content"
                  :key="analysisIndex"
                  class="text-gray-700 leading-relaxed"
                >
                  {{ item }}
                </div>
              </div>
            </div>

            <!-- 知识点 -->
            <div v-if="question.knowledgePoints.length > 0" class="flex flex-wrap items-center gap-2">
              <span class="text-gray-600">知识点：</span>
              <div class="flex flex-wrap gap-2">
                <NTag
                  v-for="point in question.knowledgePoints"
                  :key="point"
                  type="info"
                >
                  {{ point }}
                </NTag>
              </div>
            </div>
          </div>
        </div>
      </NScrollbar>
    </div>

    <!-- 内容为空状态 -->
    <div v-else class="text-group_3 empty h-full flex flex-col items-center justify-center">
      <img src="@/assets/imgs/empty.png" alt="" class="h-272px w-272px">
      <span class="text-center text-20px">出题指南</span>
      <span class="mt-14px text-center text-18px text-[rgba(172,172,172,1)] font-normal">
        1.选择出题方式，境好必要的信息<br>
        2.系统将自动保存您的出题设置<br>
        3.&nbsp;3. 点击”立即出题”，题目结果将显示在右侧
      </span>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
